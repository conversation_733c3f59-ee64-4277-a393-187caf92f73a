# CAMP<PERSON>IGN SCRIPT LOADING FIX PLAN

## CRITICAL ISSUES IDENTIFIED:

### 1. **Script Loading Issues:**
- **Problem**: No script bug - campaign scripts not loading properly in all flows
- **Root Cause**: Different config retrieval mechanisms across flows
- **Impact**: AI agent starts without proper instructions

### 2. **Inconsistent Implementation Patterns:**
- Twilio flows use `twilio-flow-handler.js` with one pattern
- Testing flows use `local-testing-handler.js` with another pattern
- Different session creation methods between flows
- Duplicate code for Live API setup

### 3. **Key Differences Between Flows:**

**A. Session Creation:**
- **Twilio flows**: Use `sessionManager.createGeminiSession()` in `twilio-flow-handler.js:288`
- **Testing flows**: Use `geminiClient.live.connect()` directly in `local-testing-handler.js:414`

**B. Live API Setup:**
- **Twilio flows**: Setup sent in `handleTwilioStartSession()` at line 359-381
- **Testing flows**: Setup sent in two places - `start-session.js:174-196` AND `local-testing-handler.js:375-397`

**C. Config Retrieval:**
- **Twilio flows**: Rely on stored config from webhooks via `getNextCallConfig()`
- **Testing flows**: Use script manager directly without webhook config check

## DETAILED FIX PLAN:

### **PHASE 1: Fix Campaign Script Loading** ✅ COMPLETED

1. **Fix Inbound Twilio Config Loading** (`config-handlers.js`): ✅
   - Line 45-63: The inbound config handler checks for stored config but has complex logic
   - **Action**: Simplify to match outbound pattern - always check stored config first
   - **Critical**: Remove the complex isInboundConfig check that might fail
   - **STATUS**: COMPLETED - Simplified logic implemented at lines 46-56

2. **Add Webhook Config Storage for Inbound**: ⚠️ NEEDS VERIFICATION
   - **Problem**: Inbound calls might not have stored config from webhook
   - **Action**: Ensure `/incoming-call` webhook stores config like `/make-call` does
   - **Location**: Check webhook handlers that set config before WebSocket connection

3. **Fix Testing Config Fallbacks**: ✅ COMPLETED
   - **Problem**: Testing configs fall back to hardcoded instructions when scripts fail to load
   - **Action**: Always load real campaign scripts, never use hardcoded fallbacks
   - Lines 163-183 in `config-handlers.js` (outbound test) and 230-240 (inbound test)
   - **STATUS**: COMPLETED - Now throws errors if no scripts available (lines 156, 204)

### **PHASE 2: Harmonize Session Creation** ✅ COMPLETED

1. **Standardize on SessionManager Pattern**: ✅
   - **Best Practice**: Use `sessionManager.createGeminiSession()` for ALL flows
   - **Action**: Replace direct `geminiClient.live.connect()` calls in testing flows
   - **Location**: `local-testing-handler.js:414` - replace with sessionManager call
   - **STATUS**: COMPLETED - Testing flows now use sessionManager at line 335

2. **Remove Duplicate Live API Setup**: ✅ PARTIALLY COMPLETED
   - **Problem**: Testing flows send setup in multiple places
   - **Action**: Remove setup code from `start-session.js:174-196`
   - **Keep**: Only the setup in `local-testing-handler.js` OR move all to sessionManager
   - **STATUS**: COMPLETED in `start-session.js` (lines 159-160 show removal)
   - **ISSUE**: Need to verify testing flows actually send setup after session creation

### **PHASE 3: Unify Message Handling** ❌ NOT COMPLETED

1. **Create Unified Gemini Message Handler**:
   - **Problem**: Different message handling in `start-session.js:212-259` vs `local-testing-handler.js:430-561`
   - **Action**: Extract common handler to shared utility
   - **Include**: Audio forwarding, text logging, state updates

2. **Standardize Audio Forwarding**:
   - **Best Pattern**: From `local-testing-handler.js:493-523` (checks WebSocket state properly)
   - **Action**: Use this pattern in ALL message handlers
   - **Critical**: Always check `localWs.readyState === 1` before sending

### **PHASE 4: Fix Connection Data Structure** ✅ PARTIALLY COMPLETED

1. **Standardize Connection Data**: ✅ COMPLETED
   - **Twilio flows**: Missing properties that testing flows have (lines 272-276 vs 287-298)
   - **Action**: Add missing properties to Twilio connectionData:
     - `lastAIResponse` ✅ Added at line 28
     - `responseTimeouts` ✅ Added at line 29
     - `connectionQuality` ✅ Added at line 30
     - `lastContextSave` ✅ Added at line 31
     - `contextSaveInterval` ✅ Added at line 32

2. **Fix WebSocket References**: ❌ NOT COMPLETED
   - **Problem**: Testing uses `localWs`, Twilio uses `twilioWs`
   - **Action**: Standardize on single property name across all flows

### **PHASE 5: Implement Best Practices** ✅ PARTIALLY COMPLETED

1. **Always Get Fresh State**: ✅ COMPLETED
   - **Best Practice**: From `twilio-flow-handler.js:26-28` - "Don't use persistent session variables"
   - **Action**: Apply this pattern to testing flows
   - **Implementation**: Always use `activeConnections.get(sessionId)` for current state

2. **Proper Error Handling**: ✅ PARTIALLY COMPLETED
   - **Best Pattern**: Twilio's graceful error handling with fallback messages
   - **Action**: Add similar error handling to testing flows
   - **Include**: User-friendly error messages and proper cleanup

3. **Consistent Logging**: ✅ PARTIALLY COMPLETED
   - **Action**: Use same log format and detail level across all flows
   - **Include**: Session IDs, flow types, and state transitions

### **PHASE 6: Testing & Validation** ❌ NOT COMPLETED

1. **Create Test Matrix**:
   - Test all 4 flows with valid campaign scripts
   - Test all 4 flows with missing/invalid scripts
   - Verify proper fallback behavior

2. **Add Script Validation**:
   - Log campaign script loading at every step
   - Verify instructions are never empty
   - Add warnings for fallback usage

## **PRIORITY ORDER**:

1. **URGENT**: Fix inbound config loading (Phase 1, Step 1) ✅ COMPLETED
2. **HIGH**: Standardize session creation (Phase 2) ✅ COMPLETED
3. **HIGH**: Fix connection data structure (Phase 4) ✅ PARTIALLY COMPLETED
4. **MEDIUM**: Unify message handling (Phase 3) ❌ NOT COMPLETED
5. **LOW**: Implement remaining best practices (Phase 5) ✅ PARTIALLY COMPLETED

## **CRITICAL SUCCESS FACTORS**:

1. **Never Start Without Scripts**: All flows must have valid campaign instructions
2. **Use Same Code Paths**: Reduce code duplication by using shared utilities
3. **Consistent State Management**: All flows use activeConnections the same way
4. **Proper Error Recovery**: Handle failures gracefully without breaking conversations

## **IMPLEMENTATION CHECKLIST**:

- [x] Fix inbound config loading logic in `config-handlers.js`
- [ ] Verify webhook config storage for inbound calls
- [x] Remove hardcoded fallback instructions from testing configs
- [x] Replace direct `geminiClient.live.connect()` with `sessionManager.createGeminiSession()`
- [x] Remove duplicate Live API setup code from `start-session.js`
- [ ] Create unified Gemini message handler utility
- [ ] Standardize audio forwarding pattern across all flows
- [x] Add missing properties to Twilio connectionData
- [ ] Standardize WebSocket property names
- [x] Implement fresh state retrieval in testing flows
- [ ] Add proper error handling to testing flows
- [ ] Standardize logging across all flows
- [ ] Create and execute test matrix
- [ ] Add script validation logging

## **FILES TO MODIFY**:

1. `src/websocket/config-handlers.js` - ✅ Fix config loading logic
2. `src/websocket/local-testing-handler.js` - ✅ Standardize session creation
3. `src/websocket/start-session.js` - ✅ Remove duplicate setup code
4. `src/websocket/twilio-flow-handler.js` - ✅ Add missing connectionData properties
5. `src/session/session-manager.js` - ✅ Ensure createGeminiSession works for all flows
6. Webhook handlers (TBD) - ❓ Ensure inbound config storage

## **EXPECTED OUTCOME**:

After implementing these fixes:
1. All 4 flows will reliably load campaign scripts
2. Code will be more maintainable with less duplication
3. Consistent behavior across all flow types
4. Better error handling and recovery
5. Easier debugging with consistent logging

## **POST-AUDIT STATUS (Updated)**:

### ✅ **IMPROVEMENTS IMPLEMENTED**:

1. **Session Creation Harmonized**: ALL flows now use `sessionManager.createGeminiSession()`
2. **Config Loading Fixed**: Inbound config handler simplified
3. **Duplicate Setup Removed**: Removed from `start-session.js`
4. **Campaign Script Loading Enhanced**: Testing configs now throw errors if no scripts
5. **ConnectionData Properties Added**: Twilio flows now have all required properties

### ⚠️ **REMAINING ISSUES**:

1. **WebSocket Property Names**: Still inconsistent (`localWs` vs `twilioWs`)
2. **Live API Setup Location**: Need to verify testing flows send setup after session creation
3. **Message Handling Not Unified**: Duplicate code across handlers
4. **Audio Message Type Support**: Testing handler supports both `audio-data` and `audio`

### 🔍 **CRITICAL OBSERVATION**:

The session manager comment states instructions will be sent by flow handlers, but verification needed that testing flows actually send the Live API setup after calling `createGeminiSession()`.

### 📋 **NEXT STEPS**:

1. **Verify Live API Setup** in testing flows
2. **Standardize WebSocket Property Names**
3. **Create Unified Message Handler** utility
4. **Complete Testing Matrix**
5. **Add More Logging** for campaign script verification