# Continuous Conversation Implementation Plan for Gemini Live Audio API

## Problem Summary

The current implementation only supports single-turn conversations. After the AI responds once, it doesn't continue the conversation even though audio continues to stream. This affects all 4 flows:
- Outbound Twilio calls
- Outbound browser testing
- Inbound Twilio calls
- Inbound browser testing

## Root Cause Analysis

1. **Turn-Based Architecture**: The system sends `turnComplete: true` with initial instructions, causing Gemini to respond once and then wait
2. **No Turn Reinitiation**: After <PERSON> completes speaking, there's no mechanism to signal it should listen for the next user input
3. **Missing Voice Activity Detection (VAD) Integration**: Not utilizing Gemini's automatic VAD capabilities
4. **No Conversation State Management**: Each interaction is treated as isolated rather than part of continuous dialogue
5. **Using Wrong API Method**: Current code uses `sendClientContent` (turn-based) instead of the Live API's real-time methods

## Key Finding from Old Code Analysis

The old_index.js also used `sendClientContent` with `turnComplete: true`, which suggests the conversation flow issues may have existed there too. However, the old code had extensive keep-alive mechanisms and recovery logic that might have masked the issue.

## Implementation Plan

### 0. Critical: Switch to Live API Methods (HIGHEST Priority)

**Current Issue**: Using `sendClientContent` which is for turn-based conversations
**Solution**: Use the Live API's real-time methods properly

**The Live API uses different methods for continuous conversation**:
1. **Setup Phase**: Send initial configuration with `setup` message
2. **Instructions**: Send system instructions during setup, not as client content
3. **Audio Streaming**: Use `sendRealtimeInput` for continuous audio
4. **No Turn Management**: Don't use `turnComplete` at all

**Correct Live API Pattern**:
```javascript
// During session creation/setup
await geminiSession.send({
    setup: {
        model: 'gemini-2.5-flash-preview-native-audio-dialog',
        systemInstruction: {
            parts: [{
                text: campaignScript // Full campaign script here
            }]
        },
        voice: {
            name: voiceName
        },
        generationConfig: {
            responseModalities: ['AUDIO'],
            speechConfig: {
                voiceConfig: {
                    prebuiltVoiceConfig: {
                        voiceName: voiceName
                    }
                }
            }
        }
    }
});

// For audio streaming (continuous)
await geminiSession.sendRealtimeInput({
    media: {
        data: base64Audio,
        mimeType: 'audio/pcm;rate=16000'
    }
});
```

### 1. Remove Explicit Turn Management (High Priority)

**Current Issue**: Sending `turnComplete: true` in initial setup messages
**Solution**: Remove ALL uses of `sendClientContent` and `turnComplete`

**Files to modify**:
- `src/websocket/local-testing-handler.js` (lines 349-372 - replace sendClientContent)
- `src/websocket/twilio-flow-handler.js` (similar pattern)
- `src/websocket/config-handlers.js` (any sendClientContent usage)
- `src/websocket/start-session.js` (createGeminiSession function)
- `src/session/session-manager.js` (any sendClientContent usage)

**Changes**:
```javascript
// WRONG - Remove this entire pattern:
await activeSession.sendClientContent({
    turns: [{
        role: 'user',
        parts: [{
            text: combinedMessage
        }]
    }],
    turnComplete: true
});

// CORRECT - Use setup message instead:
await geminiSession.send({
    setup: {
        model: modelName,
        systemInstruction: {
            parts: [{
                text: campaignScript  // Full campaign script, not a trigger message
            }]
        },
        voice: {
            name: voiceName
        },
        generationConfig: {
            responseModalities: ['AUDIO'],
            speechConfig: {
                voiceConfig: {
                    prebuiltVoiceConfig: {
                        voiceName: voiceName
                    }
                }
            }
        }
    }
});
```

### 2. Enable Voice Activity Detection (High Priority)

**Purpose**: Let Gemini automatically detect when users start/stop speaking
**Implementation**: Configure VAD in the setup message

**Correct Live API configuration**:
```javascript
await geminiSession.send({
    setup: {
        model: 'gemini-2.5-flash-preview-native-audio-dialog',
        systemInstruction: {
            parts: [{
                text: campaignScript
            }]
        },
        voice: {
            name: voiceName
        },
        generationConfig: {
            responseModalities: ['AUDIO'],
            speechConfig: {
                voiceConfig: {
                    prebuiltVoiceConfig: {
                        voiceName: voiceName
                    }
                }
            }
        },
        tools: [] // Add if needed
    }
});

// After setup is complete, configure real-time input
await geminiSession.send({
    realtimeInput: {
        mediaChunks: [{
            mimeType: 'audio/pcm;rate=16000',
            data: '' // Empty initial data
        }]
    }
});
```

**Note**: The Live API handles VAD automatically when using the native audio dialog model. You don't need to explicitly configure VAD parameters - the model will detect speech starts/stops and handle interruptions naturally.

### 3. Implement Continuous Audio Streaming (High Priority)

**Current Issue**: Audio is sent but not properly managed for continuous conversation
**Solution**: Use the Live API's `sendRealtimeInput` method correctly

**Current WORKING pattern (keep this)**:
```javascript
// This is already correct in the codebase:
await geminiSession.sendRealtimeInput({
    media: {
        data: base64Audio,
        mimeType: 'audio/pcm;rate=16000'
    }
});
```

**Important**: The current audio streaming implementation is actually correct! The issue is not with audio streaming but with the initial setup using `sendClientContent`. Once we fix the setup phase, the existing audio streaming should work for continuous conversation.

**No changes needed for audio streaming** - the current implementation in:
- `src/session/session-manager.js` (sendBrowserAudioToGemini method)
- `src/session/session-manager.js` (sendAudioToGemini method)

These methods already use `sendRealtimeInput` correctly.

### 4. Update Campaign Scripts (Medium Priority)

**Current Issue**: Scripts contain trigger phrases that are being sent as separate messages
**Solution**: Send the full campaign script as system instruction, not as user messages

**Key Change**: Stop sending trigger messages like:
- "The call has been answered. Start speaking immediately according to your outbound campaign instructions."
- "A customer has just called you. Greet them warmly and ask how you can help them today. Start speaking immediately."

**Instead**: The campaign script itself should contain all necessary context:
```javascript
// For outbound scripts, include in the script:
"You are making an outbound call. When the call connects, introduce yourself and begin the conversation according to the script below..."

// For inbound scripts, include in the script:
"You are receiving an inbound call. When the customer speaks, greet them warmly and assist them according to the script below..."
```

**Files to update**:
- Remove trigger message generation from `src/websocket/local-testing-handler.js`
- Remove trigger message generation from `src/websocket/twilio-flow-handler.js`
- Ensure campaign scripts in the database include proper context

### 5. Handle Turn Complete Events Properly (Medium Priority)

**Current Issue**: `turnComplete` events in Live API work differently
**Solution**: In continuous conversation mode, turns are managed automatically

**With the Live API in continuous mode**:
- The model automatically handles turn-taking based on VAD
- `turnComplete` events may not be sent in the same way
- The conversation flows naturally without explicit turn management

**What to expect**:
```javascript
// The model will send various events:
if (message.serverContent?.modelTurn) {
    // AI is speaking
}
if (message.serverContent?.interrupted) {
    // User interrupted the AI
}
// Audio will flow continuously without explicit turn boundaries
```

**Files to update**:
- Update event handlers in `src/websocket/local-testing-handler.js`
- Update event handlers in `src/session/session-manager.js`
- Remove any logic that depends on explicit turn management

### 6. Implement Proper Session Lifecycle (Low Priority)

**Enhancement**: Better session management for long conversations

**Features to add**:
1. Session timeout extension (default 10-30 minutes)
2. Conversation context preservation
3. Graceful reconnection handling

### 7. Audio Format Optimization (Low Priority)

**Current**: Using 16kHz for input
**Recommended**: Match Gemini's native format
- Input: 16-bit PCM @ 16kHz (current is correct)
- Output: 16-bit PCM @ 24kHz (ensure proper handling)

## Implementation Order

1. **Phase 1 - Core Fix** (Immediate):
   - Replace ALL `sendClientContent` calls with proper `setup` messages
   - Move campaign scripts to `systemInstruction` in setup
   - Remove all `turnComplete: true` usage
   - Test single flow (browser testing) first

2. **Phase 2 - Verify Audio Streaming** (Next):
   - Confirm `sendRealtimeInput` is working correctly (it should be)
   - Test continuous conversation with all 4 flows
   - Monitor for any audio quality issues

3. **Phase 3 - Polish** (Later):
   - Update campaign scripts to include proper context
   - Remove trigger message generation
   - Update event handlers for Live API events

## Testing Plan

1. **Browser Testing Flow First**: Easier to debug without Twilio complexity
2. **Test Scenarios**:
   - Multiple back-and-forth exchanges
   - Interruption handling
   - Long pauses between speaking
   - Concurrent speech handling

3. **Success Criteria**:
   - AI responds to multiple user inputs
   - Natural conversation flow
   - Proper handling of silence
   - No dropped responses

## Key Resources

- [Google Live API Documentation](https://ai.google.dev/gemini-api/docs/live)
- [Interactive Conversations Guide](https://cloud.google.com/vertex-ai/generative-ai/docs/live-api/streamed-conversations)
- Model to use: `gemini-2.5-flash-preview-native-audio-dialog`

## Common Pitfalls to Avoid

1. Don't send `turnComplete` unless ending conversation
2. Don't restart sessions between turns
3. Don't buffer too much audio before sending
4. Don't ignore VAD configuration
5. Don't mix turn-based and continuous modes

## Expected Outcome

After implementation, all 4 flows should support:
- Natural, continuous conversations
- Multiple back-and-forth exchanges
- Interruption handling
- Smooth conversation flow without manual turn management

## Quick Reference: Main Changes

1. **Session Creation** (`src/websocket/start-session.js`):
   - Replace `geminiSession.sendClientContent()` with proper setup
   - Send campaign script as `systemInstruction` during setup

2. **Local Testing Handler** (`src/websocket/local-testing-handler.js`):
   - Remove lines 349-374 (sendClientContent calls)
   - Replace with setup configuration after session creation

3. **Twilio Flow Handler** (`src/websocket/twilio-flow-handler.js`):
   - Similar changes as local testing handler
   - Remove sendClientContent, use setup instead

4. **Session Manager** (`src/session/session-manager.js`):
   - Keep `sendRealtimeInput` for audio (already correct)
   - Remove any `sendClientContent` usage

5. **No changes needed**:
   - Audio streaming methods (already using `sendRealtimeInput`)
   - Audio processing/conversion logic
   - WebSocket connection handling

## Example Implementation

Here's what the corrected session initialization should look like:

```javascript
// In createGeminiSession function
const geminiSession = await deps.sessionManager.geminiClient.live.connect({
    model: correctModel,
    callbacks: {
        onopen: async () => {
            isSessionActive = true;
            
            // Send setup configuration with campaign script
            await geminiSession.send({
                setup: {
                    model: correctModel,
                    systemInstruction: {
                        parts: [{
                            text: sessionConfig.aiInstructions // Full campaign script
                        }]
                    },
                    voice: {
                        name: correctVoice
                    },
                    generationConfig: {
                        responseModalities: ['AUDIO'],
                        speechConfig: {
                            voiceConfig: {
                                prebuiltVoiceConfig: {
                                    voiceName: correctVoice
                                }
                            }
                        }
                    }
                }
            });
        },
        // ... other callbacks
    }
});
```

This approach ensures the AI has all context from the start and can engage in continuous conversation without waiting for turn-based triggers.